import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useState } from "react";

interface ContactFormProps {
  contactInfo: { email: string; phone: string };
  onContactInfoChange: (info: { email: string; phone: string }) => void;
  userName?: string; // Add optional userName prop
}

export default function ContactForm({ contactInfo, onContactInfoChange, userName }: ContactFormProps) {
  const [emailError, setEmailError] = useState("");
  const [phoneError, setPhoneError] = useState("");

  // Email validation function
  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  // Phone validation function
  const validatePhone = (phone: string): boolean => {
    // Remove all non-digit characters for validation
    const cleanPhone = phone.replace(/\D/g, '');
    // Accept phone numbers with 10-15 digits
    return cleanPhone.length >= 10 && cleanPhone.length <= 15;
  };

  const handleChange = (field: 'email' | 'phone', value: string) => {
    // Clear previous errors
    if (field === 'email') {
      setEmailError("");
    } else {
      setPhoneError("");
    }

    onContactInfoChange({
      ...contactInfo,
      [field]: value
    });
  };

  const handleBlur = (field: 'email' | 'phone') => {
    if (field === 'email' && contactInfo.email) {
      if (!validateEmail(contactInfo.email)) {
        setEmailError("Please enter a valid email address");
      }
    } else if (field === 'phone' && contactInfo.phone) {
      if (!validatePhone(contactInfo.phone)) {
        setPhoneError("Please enter a valid phone number (10-15 digits)");
      }
    }
  };

  return (
    <div>
      <h2 className="text-2xl md:text-3xl font-bold text-gray-800 mb-4">
        {userName ? `Thank you ${userName} for being honest about your operational challenges!` : "Thank You For Being Honest About Your Operational Challenges"}
      </h2>
      <p className="text-gray-600 mb-6">
      Your quiz result is being generated where should we send it?
      </p>
      
      <div className="space-y-4">
        <div>
          <Label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
            Email Address *
          </Label>
          <Input
            id="email"
            type="email"
            value={contactInfo.email}
            onChange={(e) => handleChange('email', e.target.value)}
            onBlur={() => handleBlur('email')}
            className={`w-full px-4 py-3 border rounded-xl focus:ring-2 focus:ring-pink-500 focus:border-pink-500 ${
              emailError ? 'border-red-500' : 'border-gray-300'
            }`}
            placeholder="<EMAIL>"
            required
          />
          {emailError && (
            <p className="text-red-500 text-sm mt-1">{emailError}</p>
          )}
        </div>
        <div>
          <Label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-2">
            WhatsApp Number *
          </Label>
          <Input
            id="phone"
            type="tel"
            value={contactInfo.phone}
            onChange={(e) => handleChange('phone', e.target.value)}
            onBlur={() => handleBlur('phone')}
            className={`w-full px-4 py-3 border rounded-xl focus:ring-2 focus:ring-pink-500 focus:border-pink-500 ${
              phoneError ? 'border-red-500' : 'border-gray-300'
            }`}
            placeholder="+****************"
            required
          />
          {phoneError && (
            <p className="text-red-500 text-sm mt-1">{phoneError}</p>
          )}
        </div>
      <p className="text-gray-600 mb-6">
        Please enter your email address and WhatsApp number to access your FREE Operational Bottleneck Analysis.
      </p>
      </div>
    </div>
  );
}
