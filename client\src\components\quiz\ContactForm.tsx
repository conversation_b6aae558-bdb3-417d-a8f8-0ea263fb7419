import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

interface ContactFormProps {
  contactInfo: { email: string; phone: string };
  onContactInfoChange: (info: { email: string; phone: string }) => void;
  userName?: string; // Add optional userName prop
}

export default function ContactForm({ contactInfo, onContactInfoChange, userName }: ContactFormProps) {
  const handleChange = (field: 'email' | 'phone', value: string) => {
    onContactInfoChange({
      ...contactInfo,
      [field]: value
    });
  };

  return (
    <div>
      <h2 className="text-2xl md:text-3xl font-bold text-gray-800 mb-4">
        {userName ? `Thank you ${userName} for being honest about your operational challenges!` : "Thank You For Being Honest About Your Operational Challenges"}
      </h2>
      <p className="text-gray-600 mb-6">
      Your quiz result is being generated where should we send it?
      </p>
      
      <div className="space-y-4">
        <div>
          <Label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
            Email Address *
          </Label>
          <Input
            id="email"
            type="email"
            value={contactInfo.email}
            onChange={(e) => handleChange('email', e.target.value)}
            className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
            placeholder="<EMAIL>"
            required
          />
        </div>
        <div>
          <Label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-2">
            WhatsApp Number *
          </Label>
          <Input
            id="phone"
            type="tel"
            value={contactInfo.phone}
            onChange={(e) => handleChange('phone', e.target.value)}
            className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
            placeholder="+****************"
            required
          />
        </div>
      <p className="text-gray-600 mb-6">
        Please enter your email address and WhatsApp number to access your FREE Operational Bottleneck Analysis.
      </p>
      </div>
    </div>
  );
}
