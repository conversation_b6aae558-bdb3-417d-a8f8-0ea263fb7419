import ContactForm from "@/components/quiz/ContactForm";
import QuizQuestion from "@/components/quiz/QuizQuestion";
import QuizResults from "@/components/quiz/QuizResults";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { completeQuiz, startQuizSession } from "@/lib/api";
import { useMutation, useQuery } from "@tanstack/react-query";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { useEffect, useState } from "react";

const questions = [
  {
    id: 1,
    text: "When you think about your ideal business operations, you want to...",
    options: [
      { label: "I want to have smooth workflows that just work", value: 10 },
      { label: "I want to focus on growing my business, not fixing problems", value: 8 },
      { label: "I want my team to be productive and happy", value: 6 },
      { label: "I want to serve more customers without operational chaos", value: 4 },
    ],
  },
  {
    id: 2,
    text: "This year, you're hoping to...",
    options: [
      { label: "I'm hoping to streamline our processes and reduce stress", value: 8 },
      { label: "I'm hoping to grow revenue without adding more headaches", value: 6 },
      { label: "I'm hoping to finally solve these recurring operational issues", value: 4 },
      { label: "I'm hoping to create systems that work reliably", value: 2 },
    ],
  },
  {
    id: 3,
    text: "You'd love for your team to say...",
    options: [
      { label: "Our systems just work - no more daily frustrations", value: 10 },
      { label: "I can focus on important work instead of workarounds", value: 8 },
      { label: "Finally, our processes make sense and save time", value: 6 },
      { label: "This business runs smoothly, even when the owner isn't here", value: 4 },
    ],
  },
  {
    id: 4,
    text: "Right now, you tend to...",
    options: [
      { label: "I tend to spend too much time on manual tasks", value: 2 },
      { label: "I tend to be the person everyone comes to when systems break", value: 4 },
      { label: "Sometimes I feel like we're constantly putting out fires", value: 6 },
      { label: "I tend to wonder if there's a better way to do things", value: 8 },
    ],
  },
  {
    id: 5,
    text: "Most days, I find myself...",
    options: [
      { label: "I find myself repeating the same manual tasks over and over", value: 2 },
      { label: "I find myself explaining workarounds to my team", value: 4 },
      { label: "I find myself frustrated with how long simple tasks take", value: 6 },
      { label: "I find myself wishing our systems talked to each other", value: 8 },
    ],
  },
  {
    id: 6,
    text: "When it comes to our current processes, I notice...",
    options: [
      { label: "I notice my team spending too much time on data entry", value: 2 },
      { label: "I notice the same problems coming up week after week", value: 4 },
      { label: "I notice we're losing time on tasks that should be automated", value: 6 },
      { label: "I notice frustration building when our systems don't work together", value: 8 },
    ],
  },
  {
    id: 7,
    text: "If I'm being honest about our biggest operational problem, I am...",
    options: [
      { label: "I am tired of the same workflow breaking down every week", value: 3 },
      { label: "I am frustrated that we can't grow because our processes can't handle it", value: 6 },
      { label: "I am embarrassed that we still do things manually that should be automated", value: 7 },
      { label: "I am stressed that one system failure could derail our entire operation", value: 9 },
    ],
  },
  {
    id: 8,
    text: "The truth is, I have been...",
    options: [
      { label: "I have been losing 10-20 hours per week to manual workarounds", value: 2 },
      { label: "I have been putting off fixing this problem for months (or years)", value: 4 },
      { label: "I have been the bottleneck because everything has to go through me", value: 6 },
      { label: "I have been avoiding growth because our systems can't handle more volume", value: 8 },
    ],
  },
  {
    id: 9,
    text: "Because of this ongoing problem, I struggle with...",
    options: [
      { label: "I struggle with finding time to work ON my business instead of IN it", value: 2 },
      { label: "I struggle with team morale because everyone's frustrated with our processes", value: 4 },
      { label: "I struggle with the fear that we'll lose customers due to operational delays", value: 6 },
      { label: "I struggle with the guilt that I haven't fixed this problem that affects everyone", value: 8 },
    ],
  },
  {
    id: 10,
    text: "The 1 thing I'm afraid of regarding our operations is… [Reason for asking: We need to know how to best help you overcome that]",
    options: [
      { label: "I'm afraid this problem will get worse as we try to grow", value: 2 },
      { label: "I'm afraid we'll lose a major client because our systems failed", value: 4 },
      { label: "I'm afraid my team will burn out from all these inefficient processes", value: 6 },
      { label: "I'm afraid we'll never be able to scale because our foundation is broken", value: 8 },
      { label: "Something else (type it in)", value: 5 },
    ],
  },
  {
    id: 11,
    text: "What would you like instead?",
    options: [
      { label: "Fix this one problem quickly so we can focus on growth", value: 8 },
      { label: "Get a reliable system that works without constant maintenance", value: 6 },
      { label: "Have processes that actually save time instead of creating more work", value: 4 },
      { label: "Create workflows that make my team's jobs easier and more productive", value: 2 },
    ],
  },
];

const consultationOptions = [
  { label: "Yes, I want to fix this operational problem quickly", value: "active" },
  { label: "Yes, I'd like a free bottleneck analysis", value: "analysis" },
  { label: "Maybe, I want to see my results first", value: "maybe" },
  { label: "No, I'm just exploring for now", value: "exploring" },
];

// Client-side scoring logic (mirrors former server behavior)
const businessCategories = {
  'Operational Quicksand': {
    range: [0, 35],
    description: "You're caught in The Operational Quicksand - the more you try to grow, the deeper you sink into inefficient processes!",
    problems: [
      "You're losing 15-20+ hours per week to manual workarounds and broken processes.",
      "Your team is constantly frustrated with systems that don't work together.",
      "You're avoiding growth because your processes can't handle more volume."
    ],
    recommendation: "identify and fix the ONE core workflow that's creating 80% of your operational headaches"
  },
  'Process Firefighter': {
    range: [36, 65],
    description: "You're constantly putting out operational fires instead of growing your business!",
    problems: [
      "You spend more time fixing broken workflows than working on strategy.",
      "The same operational problems keep coming up week after week.",
      "You're the bottleneck because everything has to go through you."
    ],
    recommendation: "implement one focused fix that eliminates your biggest operational bottleneck"
  },
  'Growth-Ready Optimizer': {
    range: [66, 80],
    description: "You have decent systems but they're holding back your growth potential!",
    problems: [
      "Your processes work but they're not efficient enough to scale.",
      "You notice time being wasted on tasks that should be automated.",
      "Your team could be much more productive with better workflows."
    ],
    recommendation: "streamline your key processes to unlock 10+ hours per week for growth activities"
  },
  'Efficiency Champion': {
    range: [81, 100],
    description: "You have strong operational foundations with room for advanced optimization!",
    problems: [
      "Your systems work well but could be even more efficient.",
      "There are still some manual processes that could be automated.",
      "You want to ensure your operations can handle rapid scaling."
    ],
    recommendation: "fine-tune your existing systems to achieve maximum operational efficiency"
  }
} as const;

const questionWeights: Record<number, number> = { 1: 1.2, 2: 1.5, 3: 1.3, 4: 1.1, 5: 1.2, 6: 1.4, 7: 1.0, 8: 1.2, 9: 1.3, 10: 1.1, 11: 1.0 };

function calculateScore(answers: Record<string | number, number>): number {
  let totalScore = 0;
  let maxPossibleScore = 0;

  for (let i = 1; i <= questions.length; i++) {
    const weight = questionWeights[i];
    const answer = answers[i] || 0;
    const weightedScore = answer * weight;
    totalScore += weightedScore;
    maxPossibleScore += 10 * weight;
  }

  return Math.round((totalScore / maxPossibleScore) * 100);
}

function getBusinessCategory(score: number) {
  for (const [category, data] of Object.entries(businessCategories)) {
    const [min, max] = data.range;
    if (score >= min && score <= max) {
      return { name: category, ...data } as const;
    }
  }
  const fallback = businessCategories['Process Firefighter'];
  return { name: 'Process Firefighter', ...fallback } as const;
}

export default function Quiz() {
  const [currentStep, setCurrentStep] = useState(0); // Start at 0 for welcome screen
  const [answers, setAnswers] = useState<Record<number, number>>({});
  const [contactInfo, setContactInfo] = useState({ email: "", phone: "" });
  const [userName, setUserName] = useState(""); // Add state for user's name
  const [consultationPreference, setConsultationPreference] = useState<string>("");
  const [quizResults, setQuizResults] = useState<any>(null);
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [startTime, setStartTime] = useState<string | null>(null);
  const { toast } = useToast();

  // Start quiz session when component mounts
  const { data: sessionData, isLoading: isStartingSession, error: sessionError } = useQuery({
    queryKey: ['quiz-session'],
    queryFn: startQuizSession,
    retry: 3,
    staleTime: Infinity, // Don't refetch the session
  });

  // Handle session start success/error
  useEffect(() => {
    if (sessionData) {
      setSessionId(sessionData.data.sessionId);
      setStartTime(new Date().toISOString());
      console.log('Quiz session started with ID:', sessionData.data.sessionId);
      toast({
        title: "Quiz Session Started",
        description: "Your quiz session has been initialized successfully.",
      });
    }
  }, [sessionData, setSessionId, setStartTime, toast]);

  useEffect(() => {
    if (sessionError) {
      toast({
        title: "Error",
        description: "Failed to start quiz session. Please refresh the page.",
        variant: "destructive",
      });
    }
  }, [sessionError, toast]);

  // Mutation for calling complete quiz API on each next question
  const completeQuizMutation = useMutation({
    mutationFn: completeQuiz,
    onSuccess: (data) => {
      console.log('Quiz progress saved:', data);
    },
    onError: (error) => {
      console.error('Failed to save quiz progress:', error);
      // Don't show error toast for progress saves to avoid interrupting user flow
    },
  });

  const submitQuizMutation = useMutation({
    mutationFn: async (data: any) => {
      // Log the sessionId for future API calls
      console.log('Submitting quiz with sessionId:', sessionId);

      const score = calculateScore(data.answers as Record<number, number>);
      const category = getBusinessCategory(score);

      // TODO: Here you can make an API call to submit the quiz results using the sessionId
      // Example:
      // const response = await fetch(`${API_BASE_URL}/submit`, {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify({
      //     sessionId,
      //     answers: data.answers,
      //     contactInfo: data.contactInfo,
      //     consultationPreference: data.consultationPreference,
      //     score,
      //     category: category.name
      //   })
      // });

      // Return shape expected by <QuizResults />
      return {
        score,
        category: {
          name: category.name,
          description: category.description,
          problems: category.problems,
          recommendation: category.recommendation,
        },
      };
    },
    onSuccess: (data) => {
      setQuizResults(data);
      setCurrentStep(totalSteps + 1); // Move to results
    },
    onError: () => {
      toast({
        title: "Error",
        description: "Failed to submit quiz. Please try again.",
        variant: "destructive",
      });
    },
  });

  const totalSteps = questions.length + 3; // Welcome + Name + Questions + Contact + Consultation
  const currentQuestion = Math.min(currentStep, questions.length);
  const progressPercentage = currentStep === 0 ? 0 : Math.round(((currentStep - 1) / (totalSteps - 1)) * 100);

  const handleAnswerChange = (questionId: number, value: number) => {
    setAnswers(prev => ({ ...prev, [questionId]: value }));
  };

  // Helper function to build answers array for API
  const buildAnswersArray = () => {
    return questions.map(question => {
      const selectedValue = answers[question.id];
      const selectedOption = question.options.find(option => option.value === selectedValue);

      return {
        questionId: question.id,
        selectedOption: {
          label: selectedOption?.label || "",
          value: selectedOption?.value || 0
        }
      };
    });
  };

  const handleNext = () => {
    if (currentStep === 0) {
      // Welcome screen - move to name input
      setCurrentStep(1);
    } else if (currentStep === 1) {
      // Name input step
      if (!userName.trim()) {
        toast({
          title: "Please enter your name",
          description: "We'd like to know what to call you.",
          variant: "destructive",
        });
        return;
      }
      setCurrentStep(2); // Move to first quiz question
    } else if (currentStep <= questions.length + 1) {
      // Quiz questions (adjusted for name question)
      if (!answers[currentStep - 1]) {
        toast({
          title: "Please select an answer",
          description: "You must select an answer before continuing.",
          variant: "destructive",
        });
        return;
      }

      // Call complete quiz API on each next question
      if (sessionId && startTime) {
        const answersArray = buildAnswersArray();
        const apiData = {
          sessionId,
          startTime,
          email: contactInfo.email || "",
          phoneNumber: contactInfo.phone || "",
          name: userName || "",
          consultationOption: consultationPreference || "",
          answers: answersArray
        };
        console.log('Calling complete quiz API with data:', apiData);
        completeQuizMutation.mutate(apiData);
      }

      if (currentStep - 1 < questions.length) {
        setCurrentStep(prev => prev + 1);
      } else {
        setCurrentStep(questions.length + 2); // Move to contact form
      }
    } else if (currentStep === questions.length + 2) {
      // Contact form step
      if (!contactInfo.email || !contactInfo.phone) {
        toast({
          title: "Please fill in your contact information",
          description: "Both email and phone number are required.",
          variant: "destructive",
        });
        return;
      }

      // Call complete quiz API with contact info
      if (sessionId && startTime) {
        const answersArray = buildAnswersArray();
        completeQuizMutation.mutate({
          sessionId,
          startTime,
          email: contactInfo.email,
          phoneNumber: contactInfo.phone,
          name: userName || "",
          consultationOption: consultationPreference || "",
          answers: answersArray
        });
      }

      setCurrentStep(questions.length + 3); // Move to consultation question
    } else if (currentStep === questions.length + 3) {
      // Consultation question step
      if (!consultationPreference) {
        toast({
          title: "Please select an option",
          description: "Please indicate your preference for consultation.",
          variant: "destructive",
        });
        return;
      }

      // Call complete quiz API with final data
      if (sessionId && startTime) {
        const answersArray = buildAnswersArray();
        completeQuizMutation.mutate({
          sessionId,
          startTime,
          email: contactInfo.email,
          phoneNumber: contactInfo.phone,
          name: userName || "",
          consultationOption: consultationPreference,
          answers: answersArray
        });
      }

      // Submit quiz (client-side only)
      submitQuizMutation.mutate({
        email: contactInfo.email,
        phone: contactInfo.phone,
        name: userName || "",
        answers,
        consultationPreference,
      });
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(prev => prev - 1);
    }
  };

  const isNextDisabled = () => {
    if (currentStep === 0) {
      return false; // Welcome screen - always allow next
    } else if (currentStep === 1) {
      return !userName.trim(); // Name input - require name
    } else if (currentStep <= questions.length + 1) {
      return !answers[currentStep - 1]; // Quiz questions
    } else if (currentStep === questions.length + 2) {
      return !contactInfo.email || !contactInfo.phone; // Contact form
    } else if (currentStep === questions.length + 3) {
      return !consultationPreference; // Consultation question
    }
    return false;
  };

  const getStepTitle = () => {
    if (currentStep === 0) {
      return "Welcome";
    } else if (currentStep === 1) {
      return "Getting to Know You";
    } else if (currentStep <= questions.length + 1) {
      return `Question ${currentStep - 1} of ${questions.length}`;
    } else if (currentStep === questions.length + 2) {
      return "Contact Information";
    } else if (currentStep === questions.length + 3) {
      return "Consultation Preference";
    }
    return "Results";
  };

  return (
    <div className="min-h-screen quiz-background flex items-center justify-center p-4">
      <div className="w-full max-w-6xl mx-auto">
        <div className="quiz-card rounded-3xl shadow-2xl p-5 md:p-12">
          {/* Quiz Header */}
          <div className="text-center mb-8">
            <h1 className="text-4xl md:text-5xl font-bold gradient-text mb-4">
              Business Efficiency Assessment
            </h1>
            <p className="text-gray-600 text-lg md:text-xl font-medium">
              Discover Which Single Fix Could Save Your Business 10+ Hours This Week
            </p>
          </div>

          {/* Session Loading State */}
          {isStartingSession && (
            <div className="text-center mb-8">
              <div className="inline-flex items-center px-4 py-2 bg-blue-50 border border-blue-200 rounded-lg">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
                <span className="text-blue-700 font-medium">Initializing quiz session...</span>
              </div>
            </div>
          )}

          {/* Session Error State */}
          {sessionError && (
            <div className="text-center mb-8">
              <div className="inline-flex items-center px-4 py-2 bg-red-50 border border-red-200 rounded-lg">
                <span className="text-red-700 font-medium">Failed to start session. Please refresh the page.</span>
              </div>
            </div>
          )}

          {/* Progress Bar */}
          {currentStep > 0 && currentStep <= totalSteps && sessionId && (
            <div className="mb-8">
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm font-medium text-gray-600">
                  {getStepTitle()}
                </span>
                <span className="text-sm font-medium text-gray-600">
                  {progressPercentage}% completed
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-3">
                <div
                  className="progress-bar h-3 rounded-full transition-all duration-300"
                  style={{ width: `${progressPercentage}%` }}
                />
              </div>
            </div>
          )}

          {/* Quiz Content - Only show when session is ready */}
          {sessionId && currentStep === 0 && (
            <div className="text-center max-w-2xl mx-auto">
              {/* <h2 className="text-3xl md:text-4xl font-bold text-gray-800 mb-6">
                Welcome to the min 2-Quiz!
              </h2> */}
              {/* <p className="text-gray-600 text-lg md:text-xl mb-8">
                Discover Which Single Fix Could Save Your Business 10+ Hours This Week
              </p> */}
              <p className="text-gray-500 text-lg md:text-xl text-base">
                This quick assessment will help identify your biggest operational bottleneck and provide a personalized solution.
              </p>
            </div>
          )}

          {sessionId && currentStep === 1 && (
            <div>
              <h2 className="text-2xl md:text-3xl font-bold text-gray-800 mb-6">
                Before we go ahead, what should we call you?
              </h2>
              <div className="space-y-4">
                <div>
                  <label htmlFor="userName" className="block text-sm font-medium text-gray-700 mb-2">
                    Your Name *
                  </label>
                  <input
                    id="userName"
                    type="text"
                    value={userName}
                    onChange={(e) => setUserName(e.target.value)}
                    className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-pink-500 focus:border-pink-500 text-lg"
                    placeholder="Enter your name"
                    required
                  />
                </div>
              </div>
            </div>
          )}

          {sessionId && currentStep > 1 && currentStep <= questions.length + 1 && (
            <QuizQuestion
              question={questions[currentStep - 2]}
              selectedValue={answers[currentStep - 1]}
              onAnswerChange={handleAnswerChange}
            />
          )}

          {sessionId && currentStep === questions.length + 2 && (
            <ContactForm
              contactInfo={contactInfo}
              onContactInfoChange={setContactInfo}
              userName={userName}
            />
          )}

          {sessionId && currentStep === questions.length + 3 && (
            <div>
              <h2 className="text-2xl md:text-3xl font-bold text-gray-800 mb-6">
                <span className="text-orange-500 font-extrabold">➜</span> What would you like instead?
                <span className="text-red-500">*</span>
              </h2>
              
              <div className="space-y-4">
                {consultationOptions.map((option, index) => (
                  <label
                    key={option.value}
                    className={`radio-option flex items-center p-4 border-2 border-gray-200 rounded-xl cursor-pointer transition-all duration-300 hover:bg-gray-50 hover:-translate-y-1 ${
                      consultationPreference === option.value ? 'selected bg-red-50 border-pink-500' : ''
                    }`}
                  >
                    <input
                      type="radio"
                      name="consultation"
                      value={option.value}
                      checked={consultationPreference === option.value}
                      onChange={(e) => setConsultationPreference(e.target.value)}
                      className="mr-4 text-pink-500 focus:ring-pink-500"
                    />
                    <div>
                      <span className="font-semibold text-gray-700">{String.fromCharCode(65 + index)}</span>
                      <span className="ml-2 text-gray-700">{option.label}</span>
                    </div>
                  </label>
                ))}
              </div>
              
              <p className="text-sm text-gray-500 mt-4">Choose one option</p>
            </div>
          )}

          {currentStep > totalSteps && quizResults && (
            <QuizResults results={quizResults} userName={userName} />
          )}

          {/* Navigation Buttons */}
          {sessionId && currentStep <= totalSteps && (
            <div className={`flex mt-8 ${currentStep === 0 ? 'justify-center' : 'justify-between'}`}>
              <Button
                variant="outline"
                onClick={handlePrevious}
                disabled={currentStep === 0}
                className={`px-3 py-2 md:px-4 md:py-2 ${currentStep === 0 ? 'hidden' : 'flex items-center'}`}
              >
                <ChevronLeft className="h-4 w-4 mr-2" />
                Previous
              </Button>

              <Button
                onClick={handleNext}
                disabled={isNextDisabled() || submitQuizMutation.isPending || !sessionId}
                className={`${currentStep === 0 ? 'px-8 py-4 text-xl md:px-12 md:py-6 md:text-2xl' : 'px-3 py-2 md:px-4 md:py-2'} bg-yellow-500 hover:bg-yellow-600 text-black font-bold ${currentStep === 0 ? '' : 'ml-auto'}`}
              >
                {submitQuizMutation.isPending ? (
                  "Submitting..."
                ) : currentStep === 0 ? (
                  "Start 2 min Quiz"
                ) : currentStep === 1 ? (
                  "Continue"
                ) : currentStep === questions.length + 3 ? (
                  "Complete Quiz"
                ) : currentStep === questions.length + 2 ? (
                  "Continue"
                ) : (
                  <>
                    Next Question
                    <ChevronRight className="h-4 w-4 ml-2" />
                  </>
                )}
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
