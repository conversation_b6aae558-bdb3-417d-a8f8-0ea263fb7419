const API_BASE_URL = 'https://api.mosy.in/13.126.190.87:3006/api/business-quiz';

export interface SessionStartResponse {
  status: string;
  data: {
    success: boolean;
    sessionId: string;
    message: string;
  };
}

export interface QuizAnswer {
  questionId: number;
  selectedOption: {
    label: string;
    value: number;
  };
}

export interface CompleteQuizRequest {
  sessionId: string;
  startTime: string;
  email: string;
  phoneNumber: string;
  name: string;
  consultationOption: string;
  answers: QuizAnswer[];
}

export interface CompleteQuizResponse {
  status: string;
  data: any;
}

export const startQuizSession = async (): Promise<SessionStartResponse> => {
  const response = await fetch(`${API_BASE_URL}/start`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({}),
  });

  if (!response.ok) {
    throw new Error(`Failed to start quiz session: ${response.statusText}`);
  }

  return response.json();
};

export const completeQuiz = async (data: CompleteQuizRequest): Promise<CompleteQuizResponse> => {
  const response = await fetch(`${API_BASE_URL}/complete`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    throw new Error(`Failed to complete quiz: ${response.statusText}`);
  }

  return response.json();
};
