# Business Assessment Quiz Application

## Overview

This is a full-stack web application that provides a business assessment quiz to help entrepreneurs and business owners evaluate their current business stage and receive personalized recommendations. The application features a multi-step quiz interface where users answer questions about their business, provide contact information, and receive categorized results with actionable insights.

## User Preferences

Preferred communication style: Simple, everyday language.

## System Architecture

### Frontend Architecture
- **Framework**: React 18 with TypeScript using Vite as the build tool
- **Routing**: Wouter for client-side routing (lightweight alternative to React Router)
- **UI Components**: Shadcn/UI component library built on Radix UI primitives with Tailwind CSS
- **State Management**: React Query (TanStack Query) for server state management and API calls
- **Styling**: Tailwind CSS with custom CSS variables for theming and responsive design
- **Form Handling**: React Hook Form with Zod validation resolvers

### Backend Architecture
- **Runtime**: Node.js with Express.js framework
- **Language**: TypeScript with ES modules
- **Development**: tsx for TypeScript execution in development
- **Production Build**: esbuild for fast bundling with platform-specific optimizations
- **API Design**: RESTful endpoints with JSON request/response format
- **Error Handling**: Centralized error handling middleware with proper HTTP status codes

### Data Storage Solutions
- **Database**: PostgreSQL configured via Drizzle ORM
- **Schema Management**: Drizzle Kit for migrations and schema management
- **Connection**: Neon Database serverless PostgreSQL (indicated by @neondatabase/serverless)
- **Development Storage**: In-memory storage implementation for development/testing
- **Data Models**: User management and quiz response storage with proper typing

### Authentication and Authorization
- **Session Management**: Express sessions with PostgreSQL session store (connect-pg-simple)
- **User Model**: Basic username/password authentication system
- **Security**: Session-based authentication with HTTP-only cookies

### External Dependencies

#### Database and ORM
- **Drizzle ORM**: Type-safe database operations with PostgreSQL dialect
- **Drizzle Zod**: Schema validation integration
- **Neon Database**: Serverless PostgreSQL hosting

#### Frontend Libraries
- **UI Framework**: Comprehensive Radix UI component collection for accessible components
- **Styling**: Tailwind CSS with PostCSS for processing
- **Icons**: Lucide React for consistent iconography
- **Date Handling**: date-fns for date manipulation
- **Utilities**: clsx and class-variance-authority for conditional styling

#### Development Tools
- **Build Tools**: Vite with React plugin and runtime error overlay
- **TypeScript**: Full TypeScript support with strict configuration
- **Linting**: Configuration for code quality and consistency
- **Development Server**: Hot module replacement and fast refresh

#### Business Logic
- **Scoring System**: Multi-category business assessment with weighted scoring
- **Result Categories**: Four distinct business stages (Struggling Starter, Growing Hustler, Scaling Hustler, Business Pro)
- **Contact Collection**: Email and phone number collection for lead generation
- **Result Delivery**: Personalized recommendations based on quiz performance