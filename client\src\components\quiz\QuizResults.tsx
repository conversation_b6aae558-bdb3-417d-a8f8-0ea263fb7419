import { useEffect, useState } from "react";
import { Button } from "@/components/ui/button";
import { Star } from "lucide-react";
import CircularProgress from "./CircularProgress";

interface QuizResultsProps {
  results: {
    score: number;
    category: {
      name: string;
      description: string;
      problems: string[];
      recommendation: string;
    };
  };
  userName?: string; // Add optional userName prop
}

export default function QuizResults({ results, userName }: QuizResultsProps) {
  const [animatedScore, setAnimatedScore] = useState(0);

  useEffect(() => {
    const timer = setInterval(() => {
      setAnimatedScore(prev => {
        if (prev < results.score) {
          return Math.min(prev + 2, results.score);
        }
        clearInterval(timer);
        return results.score;
      });
    }, 50);

    return () => clearInterval(timer);
  }, [results.score]);

  return (
    <div>
      <div className="text-center mb-8">
        <h1 className="text-4xl md:text-5xl font-bold gradient-text mb-4">
          Your Efficiency Assessment Result:
        </h1>
      </div>

      <div className="">
        {/* Score Circle */}
        <div className="flex flex-col items-center">
          <CircularProgress score={animatedScore} />
        </div>

        {/* Result Content (Recommendations moved here) */}
        <div className="flex flex-col justify-center">
          <div className="space-y-4 text-gray-700 text-lg">
            <p>
              {userName ? `Thank you ${userName} for being honest about your operational challenges.` : "Thank you for being honest about your operational challenges."} You're not alone - over 78% of growing businesses struggle with the same workflow breakdowns you just described.
            </p>
            <p>
              <strong>Here's what you need to know:</strong> Based on your answers, you're caught in what we call <strong>'The Operational Quicksand'</strong> - the more you try to grow, the deeper you sink into inefficient processes.
            </p>
            <p>
              <strong>Here's why this keeps happening:</strong> Most businesses try to fix everything at once, which creates more chaos. Meanwhile, there's usually <strong>ONE core workflow</strong> that's the root cause of 80% of your operational headaches. Fix that one thing, and everything else starts working better.
            </p>
            <p>
              <strong>Here's the good news:</strong> You don't need to overhaul your entire operation. You just need to identify and fix the <strong>ONE workflow</strong> that's creating all the downstream problems.
            </p>
            <p>
              That's exactly what our <strong>Fix-One-Thing</strong> service does. In just 2-3 weeks, we identify your biggest operational bottleneck and fix it permanently. No 6-month projects, no massive disruptions, just one focused fix that gives you back <strong>10-20 hours per week</strong>.
            </p>
          </div>
        </div>
      </div>

      {/* Benefits */}
      <div className="bg-gray-50 rounded-2xl p-6 mb-8">
        <h3 className="text-2xl font-bold text-gray-800 mb-4 text-center">What You Get:</h3>
        <ul className="space-y-3 text-gray-700">
          <li className="flex items-start">
            <span className="text-green-500 mr-3 mt-1">✅</span>
            <span>One painful workflow fixed in 2-3 weeks</span>
          </li>
          <li className="flex items-start">
            <span className="text-green-500 mr-3 mt-1">✅</span>
            <span>10-20 hours per week saved immediately</span>
          </li>
          <li className="flex items-start">
            <span className="text-green-500 mr-3 mt-1">✅</span>
            <span>Training videos so your team knows exactly how to use it</span>
          </li>
          <li className="flex items-start">
            <span className="text-green-500 mr-3 mt-1">✅</span>
            <span>30-day "use it or we rework it" guarantee</span>
          </li>
        </ul>
      </div>

      {/* Call to Action */}
      <div className="text-center">
      <div className="submit-btn cta-button text-white font-bold px-8 py-8 rounded-2xl text-xl mb-4 inline-block hover:shadow-lg transition-all duration-300 overflow-hidden text-ellipsis whitespace-nowrap" >
          <a href="https://calendly.com/dilipwk/the-next-step" target="_blank" rel="noopener noreferrer">
            Get Your FREE Operational Bottleneck Analysis
          </a>
        </div>
        <p className="text-lg ">(Let's identify the exact ONE thing that's costing you the most time and money)</p>
      </div>
    </div>
  );
}
