Stack trace:
Frame         Function      Args
0007FFFFB740  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFFA640) msys-2.0.dll+0x1FE8E
0007FFFFB740  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBA18) msys-2.0.dll+0x67F9
0007FFFFB740  000210046832 (000210286019, 0007FFFFB5F8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFB740  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFB740  000210068E24 (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFBA20  00021006A225 (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFF81730000 ntdll.dll
7FFF7F3C0000 KERNEL32.DLL
7FFF7E9F0000 KERNELBASE.dll
7FFF806D0000 USER32.dll
7FFF7E9C0000 win32u.dll
7FFF814A0000 GDI32.dll
7FFF7EEF0000 gdi32full.dll
7FFF7E920000 msvcp_win.dll
7FFF7EDD0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFF81580000 advapi32.dll
7FFF7F660000 msvcrt.dll
7FFF81640000 sechost.dll
7FFF7F390000 bcrypt.dll
7FFF7FFC0000 RPCRT4.dll
7FFF7DF40000 CRYPTBASE.DLL
7FFF7E8A0000 bcryptPrimitives.dll
7FFF80530000 IMM32.DLL
