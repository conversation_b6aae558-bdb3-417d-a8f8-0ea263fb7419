interface QuizQuestionProps {
  question: {
    id: number;
    text: string;
    options: Array<{ label: string; value: number }>;
  };
  selectedValue?: number;
  onAnswerChange: (questionId: number, value: number) => void;
}

export default function QuizQuestion({ question, selectedValue, onAnswerChange }: QuizQuestionProps) {
  return (
    <div>
      <h2 className="text-2xl md:text-3xl font-bold text-gray-800 mb-6">
        <span className="text-orange-500 font-extrabold">{question.id} ➜</span> {question.text}
        <span className="text-red-500">*</span>
      </h2>
      
      <div className="space-y-4">
        {question.options.map((option, index) => (
          <label
            key={option.value}
            className={`radio-option flex items-center p-2 md:p-3 border-2 border-gray-200 rounded-xl cursor-pointer transition-all duration-300 hover:bg-gray-50 hover:-translate-y-1 ${
              selectedValue === option.value ? 'selected bg-red-50 border-pink-500' : ''
            }`}
          >
            <div>
            <input
              type="radio"
              name={`q${question.id}`}
              value={option.value}
              checked={selectedValue === option.value}
              onChange={() => onAnswerChange(question.id, option.value)}
              className="mr-1 md:mr-4 text-pink-500 focus:ring-pink-500"
            />
            </div>
            <div>
              <span className="font-semibold text-gray-700">{String.fromCharCode(65 + index)}</span>
              <span className="ml-2 text-gray-700">{option.label}</span>
            </div>
          </label>
        ))}
      </div>
      
      <p className="text-sm text-gray-500 mt-4">Choose one option</p>
    </div>
  );
}
