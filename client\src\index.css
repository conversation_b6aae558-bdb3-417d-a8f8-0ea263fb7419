@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: hsl(0, 0%, 100%);
  --foreground: hsl(210, 25%, 7.8431%);
  --card: hsl(180, 6.6667%, 97.0588%);
  --card-foreground: hsl(210, 25%, 7.8431%);
  --popover: hsl(0, 0%, 100%);
  --popover-foreground: hsl(210, 25%, 7.8431%);
  --primary: hsl(203.8863, 88.2845%, 53.1373%);
  --primary-foreground: hsl(0, 0%, 100%);
  --secondary: hsl(210, 25%, 7.8431%);
  --secondary-foreground: hsl(0, 0%, 100%);
  --muted: hsl(240, 1.9608%, 90%);
  --muted-foreground: hsl(210, 25%, 7.8431%);
  --accent: hsl(211.5789, 51.3514%, 92.7451%);
  --accent-foreground: hsl(203.8863, 88.2845%, 53.1373%);
  --destructive: hsl(356.3033, 90.5579%, 54.3137%);
  --destructive-foreground: hsl(0, 0%, 100%);
  --border: hsl(201.4286, 30.4348%, 90.9804%);
  --input: hsl(200, 23.0769%, 97.4510%);
  --ring: hsl(202.8169, 89.1213%, 53.1373%);
  --chart-1: hsl(203.8863, 88.2845%, 53.1373%);
  --chart-2: hsl(159.7826, 100%, 36.0784%);
  --chart-3: hsl(42.0290, 92.8251%, 56.2745%);
  --chart-4: hsl(147.1429, 78.5047%, 41.9608%);
  --chart-5: hsl(341.4894, 75.2000%, 50.9804%);
  --sidebar: hsl(180, 6.6667%, 97.0588%);
  --sidebar-foreground: hsl(210, 25%, 7.8431%);
  --sidebar-primary: hsl(203.8863, 88.2845%, 53.1373%);
  --sidebar-primary-foreground: hsl(0, 0%, 100%);
  --sidebar-accent: hsl(211.5789, 51.3514%, 92.7451%);
  --sidebar-accent-foreground: hsl(203.8863, 88.2845%, 53.1373%);
  --sidebar-border: hsl(205.0000, 25.0000%, 90.5882%);
  --sidebar-ring: hsl(202.8169, 89.1213%, 53.1373%);
  --font-sans: 'Inter', sans-serif;
  --font-serif: Georgia, serif;
  --font-mono: Menlo, monospace;
  --radius: 1.3rem;
  --shadow-2xs: 0px 2px 0px 0px hsl(202.8169, 89.1213%, 53.1373%, 0.00);
  --shadow-xs: 0px 2px 0px 0px hsl(202.8169, 89.1213%, 53.1373%, 0.00);
  --shadow-sm: 0px 2px 0px 0px hsl(202.8169, 89.1213%, 53.1373%, 0.00), 0px 1px 2px -1px hsl(202.8169, 89.1213%, 53.1373%, 0.00);
  --shadow: 0px 2px 0px 0px hsl(202.8169, 89.1213%, 53.1373%, 0.00), 0px 1px 2px -1px hsl(202.8169, 89.1213%, 53.1373%, 0.00);
  --shadow-md: 0px 2px 0px 0px hsl(202.8169, 89.1213%, 53.1373%, 0.00), 0px 2px 4px -1px hsl(202.8169, 89.1213%, 53.1373%, 0.00);
  --shadow-lg: 0px 2px 0px 0px hsl(202.8169, 89.1213%, 53.1373%, 0.00), 0px 4px 6px -1px hsl(202.8169, 89.1213%, 53.1373%, 0.00);
  --shadow-xl: 0px 2px 0px 0px hsl(202.8169, 89.1213%, 53.1373%, 0.00), 0px 8px 10px -1px hsl(202.8169, 89.1213%, 53.1373%, 0.00);
  --shadow-2xl: 0px 2px 0px 0px hsl(202.8169, 89.1213%, 53.1373%, 0.00);
  --tracking-normal: 0em;
  --spacing: 0.25rem;

  /* Quiz-specific colors */
  --quiz-orange: hsl(15, 100%, 60%);
  --quiz-pink: hsl(340, 82%, 52%);
  --quiz-purple-deep: hsl(245, 58%, 25%);
  --quiz-red: hsl(0, 100%, 45%);
}

.dark {
  --background: hsl(0, 0%, 0%);
  --foreground: hsl(200, 6.6667%, 91.1765%);
  --card: hsl(228, 9.8039%, 10%);
  --card-foreground: hsl(0, 0%, 85.0980%);
  --popover: hsl(0, 0%, 0%);
  --popover-foreground: hsl(200, 6.6667%, 91.1765%);
  --primary: hsl(203.7736, 87.6033%, 52.5490%);
  --primary-foreground: hsl(0, 0%, 100%);
  --secondary: hsl(195.0000, 15.3846%, 94.9020%);
  --secondary-foreground: hsl(210, 25%, 7.8431%);
  --muted: hsl(0, 0%, 9.4118%);
  --muted-foreground: hsl(210, 3.3898%, 46.2745%);
  --accent: hsl(205.7143, 70%, 7.8431%);
  --accent-foreground: hsl(203.7736, 87.6033%, 52.5490%);
  --destructive: hsl(356.3033, 90.5579%, 54.3137%);
  --destructive-foreground: hsl(0, 0%, 100%);
  --border: hsl(210, 5.2632%, 14.9020%);
  --input: hsl(207.6923, 27.6596%, 18.4314%);
  --ring: hsl(202.8169, 89.1213%, 53.1373%);
  --chart-1: hsl(203.8863, 88.2845%, 53.1373%);
  --chart-2: hsl(159.7826, 100%, 36.0784%);
  --chart-3: hsl(42.0290, 92.8251%, 56.2745%);
  --chart-4: hsl(147.1429, 78.5047%, 41.9608%);
  --chart-5: hsl(341.4894, 75.2000%, 50.9804%);
  --sidebar: hsl(228, 9.8039%, 10%);
  --sidebar-foreground: hsl(0, 0%, 85.0980%);
  --sidebar-primary: hsl(202.8169, 89.1213%, 53.1373%);
  --sidebar-primary-foreground: hsl(0, 0%, 100%);
  --sidebar-accent: hsl(205.7143, 70%, 7.8431%);
  --sidebar-accent-foreground: hsl(203.7736, 87.6033%, 52.5490%);
  --sidebar-border: hsl(205.7143, 15.7895%, 26.0784%);
  --sidebar-ring: hsl(202.8169, 89.1213%, 53.1373%);
  --font-sans: 'Inter', sans-serif;
  --font-serif: Georgia, serif;
  --font-mono: Menlo, monospace;
  --radius: 1.3rem;
  --shadow-2xs: 0px 2px 0px 0px hsl(202.8169, 89.1213%, 53.1373%, 0.00);
  --shadow-xs: 0px 2px 0px 0px hsl(202.8169, 89.1213%, 53.1373%, 0.00);
  --shadow-sm: 0px 2px 0px 0px hsl(202.8169, 89.1213%, 53.1373%, 0.00), 0px 1px 2px -1px hsl(202.8169, 89.1213%, 53.1373%, 0.00);
  --shadow: 0px 2px 0px 0px hsl(202.8169, 89.1213%, 53.1373%, 0.00), 0px 1px 2px -1px hsl(202.8169, 89.1213%, 53.1373%, 0.00);
  --shadow-md: 0px 2px 0px 0px hsl(202.8169, 89.1213%, 53.1373%, 0.00), 0px 2px 4px -1px hsl(202.8169, 89.1213%, 53.1373%, 0.00);
  --shadow-lg: 0px 2px 0px 0px hsl(202.8169, 89.1213%, 53.1373%, 0.00), 0px 4px 6px -1px hsl(202.8169, 89.1213%, 53.1373%, 0.00);
  --shadow-xl: 0px 2px 0px 0px hsl(202.8169, 89.1213%, 53.1373%, 0.00), 0px 8px 10px -1px hsl(202.8169, 89.1213%, 53.1373%, 0.00);
  --shadow-2xl: 0px 2px 0px 0px hsl(202.8169, 89.1213%, 53.1373%, 0.00);
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
    font-family: 'Inter', sans-serif;
  }
}

/* Quiz-specific styles */
.quiz-background {
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #e94560 100%);
}

.gradient-text {
  background: linear-gradient(135deg, #FF6B35 0%, #E91E63 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
}

.progress-bar {
  background: linear-gradient(90deg, #FF6B35 0%, #E91E63 100%);
}

.quiz-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.radio-option {
  transition: all 0.3s ease;
  border: 2px solid #e5e7eb;
}

.radio-option:hover {
  background-color: #f8f9fa;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.radio-option.selected {
  background-color: #fef2f2;
  border-color: #ec4899;
  box-shadow: 0 0 0 3px rgba(236, 72, 153, 0.1);
}

.result-category-badge {
  background: linear-gradient(135deg, #FF6B35 0%, #E91E63 100%);
}

.cta-button {
  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
  transition: all 0.3s ease;
}

.cta-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(220, 38, 38, 0.4);
}

/* Radio button customization */
input[type="radio"] {
  appearance: none;
  width: 20px;
  height: 20px;
  border: 2px solid #e5e7eb;
  border-radius: 50%;
  background-color: white;
  position: relative;
  cursor: pointer;
}

/* Radio button checked state */

input[type="radio"]:checked {
  border-color: #ec4899;
  background-color: #ec4899;
}

input[type="radio"]:checked::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 8px;
  height: 8px;
  background-color: white;
  border-radius: 50%;
}

input[type="radio"]:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(236, 72, 153, 0.2);
}

.submit-btn{
  max-width: 600px;
  width: 100%;
}
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}